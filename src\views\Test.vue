<template>
  <div class="test-page">
    <h1>测试页面</h1>
    <p>如果你能看到这个页面，说明路由系统工作正常</p>
    <router-link to="/" class="nav-btn">返回首页</router-link>
    <router-link to="/china-map" class="nav-btn">中国地图</router-link>
  </div>
</template>

<script setup lang="ts">
console.log('测试页面已加载');
</script>

<style scoped>
.test-page {
  padding: 20px;
  color: white;
  background: #000f1e;
  min-height: 100vh;
}

.nav-btn {
  display: inline-block;
  margin: 10px;
  padding: 10px 20px;
  background: #1cccff;
  color: #000;
  text-decoration: none;
  border-radius: 8px;
  font-weight: bold;
}

.nav-btn:hover {
  background: #fff;
}
</style>
