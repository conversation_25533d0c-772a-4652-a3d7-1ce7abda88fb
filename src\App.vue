<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup lang="ts">
// App.vue 现在只作为路由容器
</script>

<style lang="less">
* {
  box-sizing: border-box;
}

html,
body {
  width: 100%;
  height: 100%;
  line-height: 1;
  margin: 0;
  padding: 0;
  overflow: hidden;
  background: #000000;
}

#app {
  width: 100%;
  height: 100%;
  color: #ffffff;
  font-size: 12px;
  font-family: "'Pingfang SC', 'SF UI Text', 'Helvetica Neue', 'Consolas'";
  overflow: hidden;
}
</style>
