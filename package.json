{"name": "vue3-echarts-map", "private": true, "version": "0.0.1", "type": "module", "scripts": {"prepare": "husky", "dev": "vite", "build": "vue-tsc && vite build", "lint": "eslint . --ext .cjs,.ts,.vue --fix"}, "dependencies": {"axios": "^1.6.8", "echarts": "^5.5.0", "echarts-gl": "^2.0.9", "lodash-es": "^4.17.21", "normalize.css": "^8.0.1", "vue": "^3.4.21", "vue-router": "^4.5.1"}, "devDependencies": {"@commitlint/cli": "^19.2.1", "@commitlint/config-conventional": "^19.1.0", "@types/lodash-es": "^4.17.12", "@types/node": "^20.11.30", "@typescript-eslint/eslint-plugin": "^6.21.0", "@vitejs/plugin-vue": "^5.0.4", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-config-standard-with-typescript": "^43.0.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-n": "^16.6.2", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-vue": "^9.24.0", "husky": "^9.0.11", "less": "^4.2.0", "lint-staged": "^15.2.2", "prettier": "3.2.5", "typescript": "~5.3.0", "vite": "^5.2.0", "vite-plugin-eslint": "^1.8.1", "vue-tsc": "^2.0.6"}, "lint-staged": {"*.{cjs,ts,vue}": ["eslint --fix"]}}